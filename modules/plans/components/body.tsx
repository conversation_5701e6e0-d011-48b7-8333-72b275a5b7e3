import { userAPI } from 'APIs';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useAppSelector } from 'store/hooks';

interface Feature {
  id: string;
  featureType: string;
  name: string;
  description: string;
  trialDurationDays: number;
  trialUsageLimit: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface PricingCardProps {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice: number;
  planType: string;
  isPopular: boolean;
  durationDays: number;
  durationType: string;
  isActive: boolean;
  features: Feature[];
}

const PlanModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  planId: string;
  planName: string;
}> = ({ isOpen, onClose, planId, planName }) => {
  const router = useRouter();

  if (!isOpen) return null;

  const handleLogin = () => {
    console.log('<PERSON><PERSON> clicked for Plan ID:', planId);
    // Navigate to login page with plan ID as query parameter
    // router.push(`/account/sign-in?planId=${planId}`);
    router.push('/account/sign-in');
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="mx-4 w-96 rounded-lg bg-white p-6">
        <h2 className="mb-4 text-xl font-semibold text-gray-900">
          Subscribe to {planName}
        </h2>
        <p className="mb-6 text-gray-600">
          Please login to continue with your subscription.
        </p>
        <div className="flex justify-end gap-3">
          <button
            onClick={handleCancel}
            className="rounded-lg border border-gray-300 px-4 py-2 text-gray-600 transition-colors hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleLogin}
            className="rounded-lg bg-[#1EA951] px-4 py-2 text-white transition-colors hover:bg-green-600"
          >
            Login
          </button>
        </div>
      </div>
    </div>
  );
};

const PricingCard: React.FC<
  PricingCardProps & { onChoosePlan: (id: string, name: string) => void }
> = ({
  id,
  name,
  price,
  originalPrice,
  durationType,
  isPopular = false,
  features,
  onChoosePlan,
}) => {
  const handleChoosePlan = () => {
    onChoosePlan(id, name);
  };

  return (
    <div
      className={`relative rounded-xl border-2 bg-white p-6 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg ${
        isPopular ? ' border-[#25D366] bg-[#E9FBF0]' : 'border-gray-200'
      }`}
    >
      {isPopular && (
        <div className="absolute top-6  -left-0   rounded-r-full bg-[#1EA951] px-2 py-1.5 text-sm text-white">
          Most Popular
        </div>
      )}
      <div className="mb-8 text-center">
        <h3 className="text-xl font-semibold text-gray-900">{name}</h3>
        <div className="mb-2 flex items-baseline justify-center gap-1">
          <span className="text-xl font-semibold text-[#1EA951]">
            BDT {price}
          </span>
          <span className="text-xl font-medium text-[#1EA951]">
            /{durationType}
          </span>
        </div>
        <div className="text-[#565C67] line-through">BDT {originalPrice}</div>
      </div>

      <div className="mb-8 space-y-4">
        {features.map((feature, index) => (
          <div key={index} className="flex items-start gap-3">
            <Image
              src="crown.png"
              alt="crown "
              width={20}
              height={20}
              className="mt-1"
            />
            <div className="flex-1 leading-relaxed ">
              <span className="font-semibold text-gray-900">
                {feature.name}
              </span>
              {feature.description && (
                <span className="text-[#41454E]"> – {feature.description}</span>
              )}
            </div>
          </div>
        ))}
      </div>

      <button
        onClick={handleChoosePlan}
        className="w-full rounded-full bg-[#1EA951] py-3 px-6 text-base font-semibold text-white transition-colors duration-300 hover:bg-green-600"
      >
        Buy Now
      </button>
    </div>
  );
};

const PricingCards: React.FC = () => {
  const router = useRouter();
  const [pricing, setPrice] = useState<PricingCardProps[]>([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<{
    id: string;
    name: string;
  } | null>(null);

  // Check if user is authenticated
  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );
  const isAuthenticated = !!token;

  const fetchPlans = async () => {
    try {
      const res = await userAPI.getPlans();
      if ('data' in res) {
        setPrice(res.data);
      } else {
        console.error(res?.error);
      }
    } catch (error) {
      console.error('Error fetching plans:', error);
    }
  };
  // useEffect(() => {
  //   // Validate session when component mounts
  //   validateSession();
  // }, []);
  useEffect(() => {
    fetchPlans();
  }, []);

  const handleChoosePlan = async (id: string, name: string) => {
    if (isAuthenticated) {
      console.log(
        'User is authenticated, processing subscription for plan ID:',
        id
      );

      try {
        const res = await userAPI.subscriptionCheckout(id, false, token);

        if ('data' in res && res.data.paymentURL) {
          // Redirect to bKash payment URL
          console.log('Redirecting to payment URL:', res.data.paymentURL);
          window.location.href = res.data.paymentURL;
        } else if ('data' in res) {
          // Handle case where payment is successful without redirect (unlikely for bKash)
          console.log('Subscription successful:', res.data);
          router.push('/subscription/success');
        } else {
          // Handle error
          console.error('Subscription failed once again:', res?.error?.message);
          const errorMessage =
            res?.error?.message || res?.error || 'Subscription failed';
          // console.log(errorMessage);
          toast.error(errorMessage, {
            containerId: 'bottom-right',
          });
        }
      } catch (error) {
        console.error('Error during subscription:', error);
      }
    } else {
      setSelectedPlan({ id, name });
      setModalOpen(true);
    }
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setSelectedPlan(null);
  };

  return (
    <>
      <div className="mx-auto flex flex-wrap justify-center gap-5 px-4 py-5">
        {pricing.map((planData, index) => (
          <div key={planData.id} className="w-full max-w-sm">
            <PricingCard {...planData} onChoosePlan={handleChoosePlan} />
          </div>
        ))}
      </div>

      <PlanModal
        isOpen={modalOpen}
        onClose={handleCloseModal}
        planId={selectedPlan?.id || ''}
        planName={selectedPlan?.name || ''}
      />
    </>
  );
};

export default PricingCards;
