import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import FacebookProvider from 'next-auth/providers/facebook';
import AppleProvider from 'next-auth/providers/apple';
const { GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, FACEBOOK_CLIENT_ID, FACEBOOK_CLIENT_SECRET, APPLE_ID, APPLE_SECRET, NEXTAUTH_SECRET } =
  process?.env;

export default NextAuth({
  providers: [
    GoogleProvider({
      clientId: GOOGLE_CLIENT_ID!,
      clientSecret: GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),
    AppleProvider({
      clientId: APPLE_ID!,
      clientSecret: APPLE_SECRET!,
      authorization: {
        params: {
          scope: 'name email',
          response_mode: 'form_post',
        },
      },
    }),
    // FacebookProvider({
    //   clientId: FACEBOOK_CLIENT_ID!,
    //   clientSecret: FACEBOOK_CLIENT_SECRET!,
    //   authorization: {
    //     params: {
    //       auth_type: 'reauthenticate'
    //     }
    //   }
    // }),
  ],
  secret: NEXTAUTH_SECRET!,
  callbacks: {
    signIn: async ({ user, account, profile }) => {
      try {
        console.log('🔐 NextAuth signIn callback triggered');
        console.log('👤 User:', { name: user.name, email: user.email });
        console.log('🏪 Provider:', account?.provider);
        console.log('🎫 Access Token:', account?.access_token ? 'Present' : 'Missing');

        // Only process for social providers (Google/Facebook/Apple)
        if (!account?.provider) {
          console.log('❌ Missing provider');
          return false;
        }

        // For Apple, we need id_token instead of access_token
        if (account.provider === 'apple' && !account?.id_token) {
          console.log('❌ Missing Apple id_token');
          return false;
        }

        // For Google/Facebook, we need access_token
        if ((account.provider === 'google' || account.provider === 'facebook') && !account?.access_token) {
          console.log('❌ Missing access token for Google/Facebook');
          return false;
        }

        // Determine the backend endpoint based on provider
        const backendBaseUrl = process.env.NEXT_PUBLIC_API_PREFIX_REST || 'https://api-dev.fitsomnia.com/api';
        let backendUrl: string;
        if (account.provider === 'google') {
          backendUrl = `${backendBaseUrl}/user-auth/google/sign-in`;
        } else if (account.provider === 'facebook') {
          backendUrl = `${backendBaseUrl}/user-auth/facebook/sign-in`;
        } else if (account.provider === 'apple') {
          backendUrl = `${backendBaseUrl}/user-auth/apple/sign-in`;
        } else {
          console.log('❌ Unsupported provider:', account.provider);
          return false;
        }

        console.log('🔄 Calling backend API:', backendUrl);

        // Prepare request body based on provider
        let requestBody: any;
        if (account.provider === 'apple') {
          requestBody = {
            idToken: account.id_token,
            name: user.name || undefined,
          };
        } else {
          requestBody = {
            accessToken: account.access_token,
          };
        }

        // Call your NestJS backend to create/authenticate user
        const response = await fetch(backendUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        });

        const data = await response.json();
        console.log('🔄 Backend response status:', response.status);
        console.log('🔄 Backend response data:', data);

        // Check if backend authentication was successful
        if ((response.status === 200 || response.status === 201) && data.data?.token) {
          console.log('✅ Backend authentication successful');
          // Store backend JWT in the account object for later use
          account.backendToken = data.data.token;
          account.backendUser = data.data.user || data.user;
          return true;
        } else {
          console.log('❌ Backend authentication failed');
          return false;
        }
      } catch (error) {
        console.error('💥 Error in signIn callback:', error);
        return false;
      }
    },
    redirect: async ({ url, baseUrl }) => {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
    session: async ({ session, user, token }) => {
      // Add account provider information to session
      if (token?.provider) {
        (session as any).account = { provider: token.provider };
      }
      // Add backend token and user data to session
      if (token?.backendToken) {
        (session as any).backendToken = token.backendToken;
      }
      if (token?.backendUser) {
        (session as any).backendUser = token.backendUser;
      }
      return session;
    },
    jwt: ({ token, account, user }) => {
      // Store access token and provider
      if (account?.access_token) {
        token.access_token = account.access_token;
      }
      if (account?.provider) {
        token.provider = account.provider;
      }
      // Store backend token and user data
      if (account?.backendToken) {
        token.backendToken = account.backendToken;
      }
      if (account?.backendUser) {
        token.backendUser = account.backendUser;
      }
      return token;
    },
  },
});
