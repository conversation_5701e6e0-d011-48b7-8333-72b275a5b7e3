import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import FacebookProvider from 'next-auth/providers/facebook';
const { GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, FACEBOOK_CLIENT_ID, FACEBOOK_CLIENT_SECRET, NEXTAUTH_SECRET } =
  process?.env;

export default NextAuth({
  providers: [
    GoogleProvider({
      clientId: GOOGLE_CLIENT_ID!,
      clientSecret: GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),
    // FacebookProvider({
    //   clientId: FACEBOOK_CLIENT_ID!,
    //   clientSecret: FACEBOOK_CLIENT_SECRET!,
    //   authorization: {
    //     params: {
    //       auth_type: 'reauthenticate'
    //     }
    //   }
    // }),
  ],
  secret: NEXTAUTH_SECRET!,
  callbacks: {
    signIn: async ({ user, account, profile }) => {
      try {
        console.log('🔐 NextAuth signIn callback triggered');
        console.log('👤 User:', { name: user.name, email: user.email });
        console.log('🏪 Provider:', account?.provider);
        console.log('🎫 Access Token:', account?.access_token ? 'Present' : 'Missing');

        // Only process for social providers (Google/Facebook)
        if (!account?.access_token || !account?.provider) {
          console.log('❌ Missing access token or provider');
          return false;
        }

        // Determine the backend endpoint based on provider
        const backendBaseUrl = process.env.NEXT_PUBLIC_API_PREFIX_REST || 'https://api-dev.fitsomnia.com/api';
        const backendUrl = account.provider === 'google'
          ? `${backendBaseUrl}/user-auth/google/sign-in`
          : `${backendBaseUrl}/user-auth/facebook/sign-in`;

        console.log('🔄 Calling backend API:', backendUrl);

        // Call your NestJS backend to create/authenticate user
        const response = await fetch(backendUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            accessToken: account.access_token,
          }),
        });

        const data = await response.json();
        console.log('🔄 Backend response status:', response.status);
        console.log('🔄 Backend response data:', data);

        // Check if backend authentication was successful
        if ((response.status === 200 || response.status === 201) && data.data?.token) {
          console.log('✅ Backend authentication successful');
          // Store backend JWT in the account object for later use
          account.backendToken = data.data.token;
          account.backendUser = data.data.user || data.user;
          return true;
        } else {
          console.log('❌ Backend authentication failed');
          return false;
        }
      } catch (error) {
        console.error('💥 Error in signIn callback:', error);
        return false;
      }
    },
    redirect: async ({ url, baseUrl }) => {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
    session: async ({ session, user, token }) => {
      // Add account provider information to session
      if (token?.provider) {
        (session as any).account = { provider: token.provider };
      }
      // Add backend token and user data to session
      if (token?.backendToken) {
        (session as any).backendToken = token.backendToken;
      }
      if (token?.backendUser) {
        (session as any).backendUser = token.backendUser;
      }
      return session;
    },
    jwt: ({ token, account, user }) => {
      // Store access token and provider
      if (account?.access_token) {
        token.access_token = account.access_token;
      }
      if (account?.provider) {
        token.provider = account.provider;
      }
      // Store backend token and user data
      if (account?.backendToken) {
        token.backendToken = account.backendToken;
      }
      if (account?.backendUser) {
        token.backendUser = account.backendUser;
      }
      return token;
    },
  },
});
