import axios from 'axios';
import { config } from 'config';

var cookie = require('cookie');

export default async function handler(req: any, res: any) {
  try {
    const { idToken, name } = req.body;
    console.log('🔑 Apple signin API called with token:', idToken ? 'Present' : 'Missing');
    console.log('👤 Apple signin with name:', name || 'Not provided');

    // Validate required fields
    if (!idToken) {
      console.log('❌ Missing idToken for Apple signin');
      res.status(400).json({
        error: 'MISSING_ID_TOKEN',
        message: 'Apple ID token is required for authentication.'
      });
      return;
    }

    // Check if backend URL is configured
    if (!config?.restPrefix) {
      console.log('❌ Backend API URL not configured');
      res.status(500).json({
        error: 'BACKEND_NOT_CONFIGURED',
        message: 'Backend authentication service is not configured.'
      });
      return;
    }

    const response = await fetch(`${config?.restPrefix}/user-auth/apple/sign-in`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        idToken,
        name,
        platform: 'web' // Backend will handle service ID vs app ID
      }),
    });

    const data = await response.json();
    console.log('🔄 Backend response:', data);
    console.log('🔄 Backend status:', response.status);

    // Handle successful authentication (backend auto-creates users)
    // Accept both 200 (OK) and 201 (Created) as success
    if ((response.status === 200 || response.status === 201) && data.data && data.data.token) {
      res.setHeader(
        'Set-Cookie',
        cookie.serialize('token', data.data.token, {
          httpOnly: true,
          maxAge: 60 * 60 * 24 * 7,
          sameSite: 'strict',
          path: '/',
        })
      );
      console.log('✅ Cookie set successfully');
      res.status(200).json(data);
      return;
    }

    // Handle authentication errors properly
    if (response.status !== 200 && response.status !== 201) {
      console.log('❌ Backend authentication failed with status:', response.status);
      res.status(response.status).json({
        error: 'AUTHENTICATION_FAILED',
        message: 'Authentication failed. Please try again.',
        originalError: data
      });
      return;
    }

    // Fallback for unexpected responses
    console.log('❌ Unexpected response format');
    res.status(400).json({
      error: 'INVALID_RESPONSE',
      message: 'Invalid response from authentication service.'
    });
    axios.defaults.headers.common['Authorization'] = `${response}`;
  } catch (error) {
    console.error('💥 Apple signin API error:', error);

    // Handle specific error types
    if (error instanceof TypeError && error.message.includes('fetch')) {
      res.status(503).json({
        error: 'BACKEND_UNAVAILABLE',
        message: 'Authentication service is temporarily unavailable. Please try again later.'
      });
    } else if (error instanceof SyntaxError) {
      res.status(502).json({
        error: 'INVALID_BACKEND_RESPONSE',
        message: 'Invalid response from authentication service.'
      });
    } else {
      res.status(500).json({
        error: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred. Please try again.'
      });
    }
  }
}
