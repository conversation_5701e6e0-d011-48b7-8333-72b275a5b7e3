const jwt = require('jsonwebtoken');
const fs = require('fs');
const path = require('path');

// Apple Developer Credentials - Update these with your values
const APPLE_CREDENTIALS = {
  teamId: 'A4K2JJ9M2K',                              // Your Team ID
  serviceId: 'com.fitsomnia.fitsomniaApp-service',   // Your Service ID
  keyId: '2K68H9M48U',                               // Your Key ID
  privateKeyFile: 'AuthKey_2K68H9M48U.p8'           // Your private key file name
};

function generateAppleClientSecret() {
  console.log('🍎 Apple Client Secret Generator');
  console.log('================================\n');

  // Check if private key file exists
  const keyPath = path.join(__dirname, APPLE_CREDENTIALS.privateKeyFile);
  
  if (!fs.existsSync(keyPath)) {
    console.error('❌ Private key file not found:', APPLE_CREDENTIALS.privateKeyFile);
    console.log('\n📥 Please download your .p8 file from Apple Developer Console and place it in the scripts/ directory');
    console.log('🔗 https://developer.apple.com/account/resources/authkeys/list');
    process.exit(1);
  }

  // Read private key
  const privateKey = fs.readFileSync(keyPath, 'utf8');
  
  // Generate JWT payload
  const now = Math.floor(Date.now() / 1000);
  const expiration = now + (6 * 30 * 24 * 60 * 60); // 6 months
  
  const payload = {
    iss: APPLE_CREDENTIALS.teamId,
    iat: now,
    exp: expiration,
    aud: 'https://appleid.apple.com',
    sub: APPLE_CREDENTIALS.serviceId
  };

  const header = {
    alg: 'ES256',
    kid: APPLE_CREDENTIALS.keyId
  };

  try {
    // Generate JWT
    const clientSecret = jwt.sign(payload, privateKey, {
      algorithm: 'ES256',
      header: header
    });

    // Display results
    console.log('✅ Apple Client Secret Generated Successfully!\n');
    console.log('📋 Copy this to your .env.local file:');
    console.log('=====================================');
    console.log(`APPLE_CLIENT_SECRET=${clientSecret}`);
    console.log('=====================================\n');
    
    console.log('📅 Token Details:');
    console.log(`   Issued: ${new Date(now * 1000).toISOString()}`);
    console.log(`   Expires: ${new Date(expiration * 1000).toISOString()}`);
    console.log(`   Valid for: 6 months\n`);
    
    console.log('🔄 Next Steps:');
    console.log('1. Copy the APPLE_CLIENT_SECRET value above');
    console.log('2. Update your .env.local file');
    console.log('3. Restart your development server');
    console.log('4. Test Apple Sign-In flow\n');
    
    console.log('🎉 Done!');
    
  } catch (error) {
    console.error('❌ Error generating JWT:', error.message);
    process.exit(1);
  }
}

// Run the generator
if (require.main === module) {
  generateAppleClientSecret();
}

module.exports = generateAppleClientSecret;
